import enum, uuid
from datetime import datetime
from django.utils.translation import gettext_lazy as _
from ninja import ModelSchema, Schema, Field
from ninja.orm import create_schema
from pydantic import BaseModel, validator, root_validator
from typing import Optional, List, Union
from vantedge.tboss.models import Location, Product, LocationProduct, Company
from vant_calcs.uom import DensityChoices
from vant_dbreplication.models import DBReplicationServer
from pydantic import BaseModel, validator, root_validator

DB_REPLICATION_FIELDS = [
    "created_by_server",
    "modified_by_server",
    "dbreplication_state",
    "dbreplication_config",
    "dbreplication_client_company",
    "additional_data",
]

NAME_CODE_ID_FIELDS = ["name", "code", "id"]


class LocationTypeEnum(str, enum.Enum):
    TRANSFER = "Transfer"
    CONTAINER = "Container"
    FACILITY = "Facility"


LocationParentSchema = create_schema(Location, fields=NAME_CODE_ID_FIELDS)
ProductSchema = create_schema(Product, fields=NAME_CODE_ID_FIELDS)
CompanySchema = create_schema(Company, fields=NAME_CODE_ID_FIELDS)
ServerSchema = create_schema(DBReplicationServer, fields=["name", "id"])


class SuccessResponse(Schema):
    message: str


class ErrorResponse(Schema):
    error: str


class BulkErrorResponse(Schema):
    error: str
    payload: dict


class LocationProductSchema(ModelSchema):
    product: ProductSchema

    # flatten?
    # product_name: str = Field(None, alias='product.name')
    # product_id: uuid.UUID = Field(None, alias='product.id')
    # product_code: str = Field(None, alias='product.code')

    # _transform_uuids = validator("product_id", allow_reuse=True)(
    #     lambda x: str(x) if x else x
    # )

    class Config:
        model = LocationProduct
        model_exclude = DB_REPLICATION_FIELDS + ["location", "product"]


class LocationSchema(ModelSchema):
    parent: Optional[LocationParentSchema]
    location_products: Optional[List[LocationProductSchema]] = Field(
        None, alias="locationproduct_set"
    )
    company_set: Optional[List[CompanySchema]]

    # flatten?
    # company_name: str = Field(None, alias='company.name')
    # company_id: uuid.UUID = Field(None, alias='company.id')
    # company_code: str = Field(None, alias='company.code')

    # _transform_uuids = validator("company_id", allow_reuse=True)(
    #     lambda x: str(x) if x else x
    # )

    location_type: Location.LocationType
    _transform_location_type = validator("location_type", allow_reuse=True)(
        lambda x: dict(Location.LocationType.choices).get(x) if x else x
    )

    class Config:
        model = Location
        model_exclude = DB_REPLICATION_FIELDS + ["products"]
        use_enum_values = True


class RelatedEntitySchema(Schema):
    name: Optional[str]
    code: Optional[str]
    id: Optional[str]

    @root_validator()
    def check_code_or_id(cls, values):
        if (values.get("code") is None) and (values.get("id") is None):
            raise ValueError(f"Either code or id is required for related entities")
        return values


class SetProductSchema(Schema):
    code: str


class CreateLocationProductSchema(Schema):
    density: float
    density_unit: DensityChoices = DensityChoices.kg_per_m3
    product: Union[RelatedEntitySchema, SetProductSchema]

    water_density: Optional[float]
    sediment_density: Optional[float]
    use_default_density: Optional[float]
    use_default_water_density: Optional[bool]
    use_default_sediment_density: Optional[bool]


class UpdateLocationProductSchema(CreateLocationProductSchema):
    id: Optional[str]
    density: Optional[float]
    density_unit: Optional[DensityChoices] = DensityChoices.kg_per_m3
    product: Optional[Union[RelatedEntitySchema, SetProductSchema]]
    use_default_density: Optional[float] = True
    use_default_water_density: Optional[bool] = False
    use_default_sediment_density: Optional[bool] = False


class AddressSchema(Schema):
    description: str
    land_description: str
    street: str
    city: str
    province: str
    country: str
    postal_code: str


class TDGSchema(Schema):
    erap: str
    chemtrec: str
    emergency_contact: str
    emergency_phone: str
    operations_phone: str


class StorageSchema(Schema):
    product_id: str
    density: float
    start_date_beginning_qty: float
    capacity: float
    unit: str


class LocationDataSchema(Schema):
    address: Optional[AddressSchema]
    tdg: Optional[TDGSchema]
    storage: Optional[StorageSchema]
    bsw_overage: Optional[float]


class LocationTypeEnumLabel(str, enum.Enum):
    TRANSFER = "Transfer"
    CONTAINER = "Container"
    FACILITY = "Facility"


class CreateLocationSchema(Schema):
    name: str
    code: str
    enabled: Optional[bool]
    effective_start_date: Optional[datetime]
    effective_end_date: Optional[datetime]
    lockout_date: Optional[datetime]
    lockout_reason: Optional[str]
    parent: RelatedEntitySchema
    company_set: Optional[List[RelatedEntitySchema]]
    location_type: LocationTypeEnumLabel = LocationTypeEnumLabel.FACILITY
    data: Optional[LocationDataSchema]
    location_products: Optional[List[UpdateLocationProductSchema]]


class UpdateLocationSchema(CreateLocationSchema):
    id: Optional[str]
    code: Optional[str]

    name: Optional[str]
    parent: Optional[RelatedEntitySchema]
    company_set: Optional[List[RelatedEntitySchema]]
    location_type: Optional[LocationTypeEnumLabel]

    @root_validator()
    def check_code_or_id(cls, values):
        if (values.get("code") is None) and (values.get("id") is None):
            raise ValueError(f"Either code or id is required for related entities")
        return values


class PatchLocationSchema(Schema):
    parent: Optional[LocationParentSchema]
    company_set: Optional[List[RelatedEntitySchema]]

    name: Optional[str]
    enabled: Optional[bool]
    effective_start_date: Optional[datetime]
    effective_end_date: Optional[datetime]
    lockout_date: Optional[datetime]
    lockout_reason: Optional[str]
    location_type: LocationTypeEnumLabel = LocationTypeEnumLabel.FACILITY
    data: Optional[LocationDataSchema]
