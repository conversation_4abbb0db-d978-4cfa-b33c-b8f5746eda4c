from typing import Optional, Literal
from pydantic import BaseModel, Field, validator
from datetime import datetime


class BaseEventData(BaseModel):
    """Base Pydantic model for all IoT event data."""

    class Config:
        extra = "forbid"  # Prevent additional fields not defined in the model


class VehicleSchema(BaseModel):
    """Pydantic model for vehicle information."""

    vehicle_type: Literal["RAILCAR", "TRUCK"] = Field(
        ..., description="Type of vehicle"
    )
    vehicle_id: str = Field(..., description="Unique identifier for the vehicle")

    @validator("vehicle_id")
    def validate_vehicle_id(cls, v):
        if not v:
            raise ValueError("Vehicle ID is required")
        return v


class SightingEventData(BaseEventData):
    """Pydantic model for sighting-related events."""

    sighting_type: Literal["ENTRY", "EXIT"] = Field(
        ..., description="Type of sighting event"
    )
    vehicle: VehicleSchema = Field(..., description="Vehicle information")


class StatusEventVehicleTransferData(BaseEventData):
    """Pydantic model for transfer-related events."""

    vehicle: VehicleSchema = Field(..., description="Vehicle information")
    start_time: Optional[datetime] = Field(None)
    end_time: Optional[datetime] = Field(None)
    quantity: Optional[float] = Field(None)
    unit: Optional[str] = Field(None)
