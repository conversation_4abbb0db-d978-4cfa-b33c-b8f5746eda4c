from django.core.exceptions import ValidationError
from django.db.models import Count
import datetime
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.contrib.auth import get_user_model
import uuid_extensions.uuid7

from vantedge.users.models import Company

User = get_user_model()


"""Models for IoT (Internet of Things) data management.

This module provides models for storing and managing IoT events and their types.
It allows tracking of various events across different company locations.
"""


class EventType(models.Model):
    """Defines types of events that can be recorded in the system.

    EventType can be company-specific or global, and can be organized in a hierarchy
    through the parent relationship.
    """

    id = models.UUIDField(
        primary_key=True, default=uuid_extensions.uuid7, editable=False
    )

    code = models.CharField(max_length=32, default="")

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Company this event type belongs to. If null, it's a global event type.",
    )
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Parent event type for hierarchical organization.",
    )
    name = models.CharField(max_length=30, help_text="Name of the event type.")
    description = models.CharField(
        max_length=255,
        default="",
        blank=True,
        help_text="Detailed description of this event type.",
    )

    # Configuration options
    max_events_per_location = models.PositiveIntegerField(
        default=1000,
        help_text="Maximum number of events to keep per location. Older events will be deleted.",
        validators=[MinValueValidator(1), MaxValueValidator(1000000)],
    )
    retention_days = models.PositiveIntegerField(
        default=90,
        help_text="Number of days to keep events before they are automatically deleted.",
        validators=[MinValueValidator(1), MaxValueValidator(3650)],
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this event type is currently active and collecting data.",
    )
    # alert_threshold = models.JSONField(
    #     null=True,
    #     blank=True,
    #     help_text="JSON configuration for alert thresholds. Format depends on the event type.",
    # )
    display_order = models.PositiveIntegerField(
        default=0,
        help_text="Order in which to display this event type in lists and dashboards.",
    )

    class Meta:
        ordering = ["display_order", "name"]
        verbose_name = "Event Type"
        verbose_name_plural = "Event Types"
        constraints = [
            models.UniqueConstraint(
                fields=["code", "company"], name="unique_code_company"
            ),
            models.UniqueConstraint(
                fields=["code"],
                condition=models.Q(company=None),
                name="unique_code_global",
            ),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        """Validate the event type data."""
        super().clean()

        # Validate that code is unique across both company-specific and global event types
        if self.code:
            # Check if there's a global event type with the same code
            global_conflict = EventType.objects.filter(code=self.code, company=None)
            if self.pk:
                global_conflict = global_conflict.exclude(pk=self.pk)

            # Check if there's a company-specific event type with the same code
            company_conflict = EventType.objects.filter(code=self.code).exclude(
                company=None
            )
            if self.pk:
                company_conflict = company_conflict.exclude(pk=self.pk)

            if self.company is None and company_conflict.exists():
                raise ValidationError(
                    {
                        "code": f"Code '{self.code}' is already used by a company-specific event type"
                    }
                )
            elif self.company is not None and global_conflict.exists():
                raise ValidationError(
                    {
                        "code": f"Code '{self.code}' is already used by a global event type"
                    }
                )

    def save(self, *args, **kwargs):
        """Override save to run validation and set default code."""
        # Set code to uppercase name if code is empty
        if not self.code and self.name:
            self.code = self.name.upper()

        self.full_clean()
        super().save(*args, **kwargs)

    def clean_old_events(self):
        """Delete old events based on retention settings.

        This method performs two types of cleanup:
        1. Deletes events older than retention_days
        2. For each location, keeps only the most recent max_events_per_location events

        Returns the number of deleted events.
        """

        # Delete events older than retention_days
        cutoff_date = timezone.now() - datetime.timedelta(days=self.retention_days)
        old_events_count = self.event_set.filter(timestamp__lt=cutoff_date).delete()[0]

        # For each location, keep only the most recent max_events_per_location events
        locations_with_excess = (
            self.event_set.values("location")
            .annotate(count=Count("id"))
            .filter(count__gt=self.max_events_per_location)
            .values_list("location", flat=True)
        )

        excess_events_count = 0
        for location_id in locations_with_excess:
            # For each location with excess events, find the timestamp of the oldest event to keep
            events_to_keep = self.event_set.filter(location_id=location_id).order_by(
                "-timestamp"
            )[: self.max_events_per_location]

            if events_to_keep.exists():
                oldest_to_keep = events_to_keep.last().timestamp
                # Delete all events older than the oldest one to keep
                deleted_count = self.event_set.filter(
                    location_id=location_id,
                    timestamp__lt=oldest_to_keep,
                ).delete()[0]
                excess_events_count += deleted_count

        return old_events_count + excess_events_count
