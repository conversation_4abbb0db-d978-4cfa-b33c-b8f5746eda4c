from django.db.models import Q
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, DjangoModelPermissions
from rest_framework.exceptions import PermissionDenied
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend


from vantedge.iot.models import EventType, Event
from vantedge.iot.api.serializers import (
    EventTypeSerializer,
    EventSerializer,
    EventCreateSerializer,
)
from vantedge.iot.api.filters import EventFilter
from vantedge.util.mixins import IDOrCodeMixin


class CompanyFilterMixin:
    """Mixin to filter querysets by the user's company."""

    company_required = True  # Must have a company to access the resource
    company_none_allowed = False  # Retrieve objects w/ no company (global objects)

    def get_queryset(self):
        queryset = super().get_queryset()

        # Check if user is authenticated and has a company
        user = self.request.user
        if not user.is_authenticated:
            return queryset.none()

        if self.company_required and not user.company:
            # Return 401 instead of empty queryset
            raise PermissionDenied(
                "User must be associated with a company to access this resource."
            )

        # Apply company filtering based on the company_mode setting
        if self.company_none_allowed:
            return queryset.filter(Q(company=user.company) | Q(company=None))
        else:
            return queryset.filter(company=user.company)

        # Default fallback to COMPANY_ONLY mode
        return queryset.filter(company=user.company)


class EventTypeViewSet(IDOrCodeMixin, CompanyFilterMixin, ModelViewSet):
    """
    ViewSet for viewing EventType instances.

    list:
    Return a list of all event types accessible to the user.

    retrieve:
    Return the given event type if accessible to the user.
    The event type can be retrieved by either its ID or code.
    """

    queryset = EventType.objects.all()
    serializer_class = EventTypeSerializer
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    filter_backends = [filters.OrderingFilter, filters.SearchFilter]
    ordering_fields = ["company__name", "display_order", "name"]
    ordering = ["company__name", "display_order", "name"]
    search_fields = ["name", "description"]
    company_none_allowed = True


class EventViewSet(CompanyFilterMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing Event instances.

    list:
    Return a list of all events for the user's company.

    retrieve:
    Return the given event if it belongs to the user's company.

    create:
    Create a new event for the user's company.

    destroy:
    Delete an event if it belongs to the user's company.
    """

    queryset = Event.objects.all()
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = EventFilter
    ordering_fields = ["timestamp"]
    ordering = ["-timestamp"]

    def get_serializer_class(self):
        """
        Return different serializers for different actions.
        """
        if self.action == "create":
            return EventCreateSerializer
        return EventSerializer

    def perform_create(self, serializer):
        """
        Create a new event with the company from the request.
        """
        # The serializer will handle setting the company and validation
        serializer.save()

    def destroy(self, request, *args, **kwargs):
        """
        Delete an event if it belongs to the user's company.
        """
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception:
            return Response(
                {"detail": "An error occurred while deleting the event."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
