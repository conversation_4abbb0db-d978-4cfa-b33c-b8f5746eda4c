# Generated by Django 4.2 on 2025-05-26 21:39

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid_extensions.uuid7
import vantedge.iot.models.event


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0031_usernote"),
        ("wheelhouse", "0118_facility_enforce_empty_car_seal_numbers_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="EventType",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid_extensions.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("code", models.CharField(default="", max_length=32)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the event type.", max_length=30
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        default="",
                        help_text="Detailed description of this event type.",
                        max_length=255,
                    ),
                ),
                (
                    "max_events_per_location",
                    models.PositiveIntegerField(
                        default=1000,
                        help_text="Maximum number of events to keep per location. Older events will be deleted.",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(1000000),
                        ],
                    ),
                ),
                (
                    "retention_days",
                    models.PositiveIntegerField(
                        default=90,
                        help_text="Number of days to keep events before they are automatically deleted.",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(3650),
                        ],
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this event type is currently active and collecting data.",
                    ),
                ),
                (
                    "display_order",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Order in which to display this event type in lists and dashboards.",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        help_text="Company this event type belongs to. If null, it's a global event type.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.company",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent event type for hierarchical organization.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="iot.eventtype",
                    ),
                ),
            ],
            options={
                "verbose_name": "Event Type",
                "verbose_name_plural": "Event Types",
                "ordering": ["display_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="Event",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When this event occurred."
                    ),
                ),
                (
                    "event_sub_type",
                    models.CharField(help_text="Sub-type of event.", max_length=25),
                ),
                (
                    "device_ref",
                    models.CharField(
                        blank=True,
                        help_text="Identifier of the device that generated this event.",
                        max_length=50,
                        null=True,
                    ),
                ),
                ("data", models.JSONField(help_text="JSON containing event details.")),
                (
                    "company",
                    models.ForeignKey(
                        help_text="Company this event belongs to.",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.company",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        help_text="Account that created this event.",
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "event_type",
                    models.ForeignKey(
                        help_text="Type of event.",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="iot.eventtype",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        blank=True,
                        help_text="Location where this event occurred.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wheelhouse.location",
                    ),
                ),
            ],
            options={
                "verbose_name": "Event",
                "verbose_name_plural": "Events",
                "ordering": ["-timestamp"],
            },
            bases=(vantedge.iot.models.event.ProxyEventMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="eventtype",
            constraint=models.UniqueConstraint(
                fields=("code", "company"), name="unique_code_company"
            ),
        ),
        migrations.AddConstraint(
            model_name="eventtype",
            constraint=models.UniqueConstraint(
                condition=models.Q(("company", None)),
                fields=("code",),
                name="unique_code_global",
            ),
        ),
        migrations.AddIndex(
            model_name="event",
            index=models.Index(
                fields=["-timestamp"], name="iot_event_timesta_83295e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="event",
            index=models.Index(
                fields=["company", "location", "event_type"],
                name="iot_event_company_a03d54_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="event",
            index=models.Index(
                fields=["event_type", "location"], name="iot_event_event_t_048966_idx"
            ),
        ),
    ]
